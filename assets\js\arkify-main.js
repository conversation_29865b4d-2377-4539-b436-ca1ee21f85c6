/**
 * Arkify-Inspired JavaScript for Monolith Design Co.
 * Handles animations, sliders, and interactive elements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initHeroSlider();
    initScrollAnimations();
    initTestimonialSlider();
    initSmoothScrolling();
    initLoadingAnimations();
    initHoverEffects();
    initFAQAccordion();
    initServiceMenu();

    /**
     * Enhanced Hero background and text slider with text breaking
     */
    function initHeroSlider() {
        const heroSection = document.querySelector('.hero-section');
        const heroImages = document.querySelectorAll('.hero-background img');
        const textSlides = document.querySelectorAll('.hero-text-slide');
        const dots = document.querySelectorAll('.dot');

        if (!heroSection || heroImages.length === 0 || textSlides.length === 0) return;

        let currentIndex = 0;
        let slideInterval;
        let isAnimating = false;

        // Process text breaking for all slides on initialization
        processAllTextBreaking();

        // Remove loading class after initial load
        setTimeout(() => {
            heroSection.classList.remove('loading');
        }, 1000);

        /**
         * Process text breaking for all text elements
         */
        function processAllTextBreaking() {
            textSlides.forEach(slide => {
                const titleElement = slide.querySelector('.hero-title');
                const subtitleElement = slide.querySelector('.hero-data');

                if (titleElement) {
                    applyTextBreaking(titleElement);
                }

                if (subtitleElement) {
                    applyTextBreaking(subtitleElement);
                }
            });
        }

        /**
         * Apply text breaking based on data attributes
         */
        function applyTextBreaking(element) {
            const breakType = element.dataset.breakType || 'none';
            const breakLimit = parseInt(element.dataset.breakLimit) || 50;
            const originalText = element.textContent.trim();

            if (breakType === 'none') {
                return;
            }

            let processedText = '';

            switch (breakType) {
                case 'character':
                    processedText = breakByCharacters(originalText, breakLimit);
                    break;
                case 'word':
                    processedText = breakByWords(originalText, breakLimit);
                    break;
                case 'manual':
                    // For manual breaks, look for | or \n in the original text
                    processedText = originalText.replace(/\|/g, '<br>').replace(/\\n/g, '<br>');
                    break;
            }

            if (processedText) {
                element.innerHTML = processedText;
            }
        }

        /**
         * Break text by character count
         */
        function breakByCharacters(text, limit) {
            if (text.length <= limit) {
                return text;
            }

            const words = text.split(' ');
            let lines = [];
            let currentLine = '';

            words.forEach(word => {
                if ((currentLine + word).length <= limit) {
                    currentLine += (currentLine ? ' ' : '') + word;
                } else {
                    if (currentLine) {
                        lines.push(currentLine);
                    }
                    currentLine = word;
                }
            });

            if (currentLine) {
                lines.push(currentLine);
            }

            return lines.join('<br>');
        }

        /**
         * Break text by word count
         */
        function breakByWords(text, limit) {
            const words = text.split(' ');
            if (words.length <= limit) {
                return text;
            }

            const lines = [];
            for (let i = 0; i < words.length; i += limit) {
                lines.push(words.slice(i, i + limit).join(' '));
            }

            return lines.join('<br>');
        }

        // Enhanced function to show specific slide with improved animations
        function showSlide(index) {
            if (isAnimating || index === currentIndex) {
                return;
            }

            isAnimating = true;

            // Remove active class from all elements
            heroImages.forEach(img => img.classList.remove('active'));
            textSlides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));

            // Update overlay for current slide
            updateSlideOverlay(index);

            // Add active class to current elements with staggered timing
            if (heroImages[index]) {
                heroImages[index].classList.add('active');
            }

            // Delay text slide activation for smoother transition
            setTimeout(() => {
                if (textSlides[index]) {
                    textSlides[index].classList.add('active');
                }
            }, 200);

            if (dots[index]) {
                dots[index].classList.add('active');
            }

            currentIndex = index;

            // Reset animation flag
            setTimeout(() => {
                isAnimating = false;
            }, 800);
        }

        /**
         * Update overlay based on slide data
         */
        function updateSlideOverlay(index) {
            const overlay = heroSection.querySelector('.hero-overlay');
            const currentImage = heroImages[index];

            if (overlay && currentImage) {
                const overlayColor = currentImage.dataset.overlayColor || '#000000';
                const overlayOpacity = currentImage.dataset.overlayOpacity || '0.5';

                overlay.style.backgroundColor = overlayColor;
                overlay.style.opacity = overlayOpacity;
            }
        }

        // Auto-slide functionality
        function nextSlide() {
            const nextIndex = (currentIndex + 1) % Math.min(heroImages.length, textSlides.length);
            showSlide(nextIndex);
        }

        // Previous slide functionality
        function prevSlide() {
            const prevIndex = (currentIndex - 1 + Math.min(heroImages.length, textSlides.length)) % Math.min(heroImages.length, textSlides.length);
            showSlide(prevIndex);
        }

        // Enhanced dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                if (index !== currentIndex) {
                    showSlide(index);
                    restartAutoPlay();
                }
            });
        });

        // Arrow navigation (if present)
        const prevArrow = heroSection.querySelector('.left-arrow, .slider-prev');
        const nextArrow = heroSection.querySelector('.right-arrow, .slider-next');

        if (prevArrow) {
            prevArrow.addEventListener('click', () => {
                prevSlide();
                restartAutoPlay();
            });
        }

        if (nextArrow) {
            nextArrow.addEventListener('click', () => {
                nextSlide();
                restartAutoPlay();
            });
        }

        // Enhanced auto-play functions with database settings
        function startAutoPlay() {
            const firstImage = heroImages[0];
            const autoPlay = firstImage ? firstImage.dataset.autoPlay !== '0' : true;
            const duration = firstImage ? parseInt(firstImage.dataset.animationDuration) * 1000 : 5000;

            if (!autoPlay || heroImages.length <= 1) return;

            stopAutoPlay(); // Clear any existing interval
            slideInterval = setInterval(nextSlide, duration);
        }

        function stopAutoPlay() {
            if (slideInterval) {
                clearInterval(slideInterval);
                slideInterval = null;
            }
        }

        function restartAutoPlay() {
            stopAutoPlay();
            setTimeout(startAutoPlay, 2000); // Restart after 2 seconds
        }

        // Enhanced pause on hover functionality
        const firstImage = heroImages[0];
        const pauseOnHover = firstImage ? firstImage.dataset.pauseOnHover !== '0' : true;

        if (pauseOnHover) {
            heroSection.addEventListener('mouseenter', stopAutoPlay);
            heroSection.addEventListener('mouseleave', startAutoPlay);
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                prevSlide();
                restartAutoPlay();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
                restartAutoPlay();
            }
        });

        // Enhanced touch/swipe support for mobile
        let touchStartX = 0;
        let touchEndX = 0;
        let touchStartY = 0;
        let touchEndY = 0;

        heroSection.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
            touchStartY = e.changedTouches[0].screenY;
        });

        heroSection.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            touchEndY = e.changedTouches[0].screenY;
            handleSwipe();
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diffX = touchStartX - touchEndX;
            const diffY = Math.abs(touchStartY - touchEndY);

            // Only process horizontal swipes (ignore vertical scrolling)
            if (Math.abs(diffX) > swipeThreshold && diffY < swipeThreshold) {
                if (diffX > 0) {
                    nextSlide(); // Swipe left - next slide
                } else {
                    prevSlide(); // Swipe right - previous slide
                }
                restartAutoPlay();
            }
        }

        // Initialize first slide and start auto-play
        showSlide(0);
        setTimeout(() => {
            startAutoPlay();
        }, 2000);
    }

    /**
     * Scroll-based animations
     */
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                }
            });
        }, observerOptions);
        
        // Observe all elements with loading class
        document.querySelectorAll('.loading').forEach(el => {
            observer.observe(el);
        });
        
        // Observe sections for fade-in effects
        document.querySelectorAll('section').forEach(section => {
            section.classList.add('loading');
            observer.observe(section);
        });
    }
    
    /**
     * Testimonial slider functionality
     */
    function initTestimonialSlider() {
        const slides = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('.slide-dot');
        const leftArrow = document.querySelector('.left-arrow');
        const rightArrow = document.querySelector('.right-arrow');
        
        if (slides.length === 0) return;
        
        let currentSlide = 0;
        
        function showSlide(index) {
            // Hide all slides
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));
            
            // Show current slide
            if (slides[index]) {
                slides[index].classList.add('active');
            }
            if (dots[index]) {
                dots[index].classList.add('active');
            }
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }
        
        // Arrow navigation
        if (rightArrow) {
            rightArrow.addEventListener('click', nextSlide);
        }
        
        if (leftArrow) {
            leftArrow.addEventListener('click', prevSlide);
        }
        
        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
            });
        });
        
        // Auto-play (optional)
        setInterval(nextSlide, 8000);
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            }
        });
    }

    /**
     * FAQ Accordion functionality
     */
    function initFAQAccordion() {
        const faqItems = document.querySelectorAll('.faq-item');

        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer');

            if (question && answer) {
                question.addEventListener('click', () => {
                    const isActive = item.classList.contains('active');

                    // Close all other FAQ items
                    faqItems.forEach(otherItem => {
                        if (otherItem !== item) {
                            otherItem.classList.remove('active');
                            const otherAnswer = otherItem.querySelector('.faq-answer');
                            if (otherAnswer) {
                                otherAnswer.style.display = 'none';
                            }
                        }
                    });

                    // Toggle current item
                    if (isActive) {
                        item.classList.remove('active');
                        answer.style.display = 'none';
                    } else {
                        item.classList.add('active');
                        answer.style.display = 'block';
                    }
                });
            }
        });
    }

    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    /**
     * Loading animations for cards and elements
     */
    function initLoadingAnimations() {
        // Stagger animation for cards
        const cardGroups = [
            '.best-inner .best-card',
            '.work-list .work-card',
            '.article-wrap .article-card',
            '.service-left .service-mini-card'
        ];
        
        cardGroups.forEach(selector => {
            const cards = document.querySelectorAll(selector);
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    }
    
    /**
     * Hover effects and interactions
     */
    function initHoverEffects() {
        // Service cards hover effect
        document.querySelectorAll('.service-mini-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(10px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
        
        // Work cards hover effect
        document.querySelectorAll('.work-card, .article-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // Button hover effects
        document.querySelectorAll('.primary-button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }
    
    /**
     * Logo carousel animation control
     */
    function initLogoCarousel() {
        const carousels = document.querySelectorAll('.gallery-wrap');
        
        carousels.forEach(carousel => {
            carousel.addEventListener('mouseenter', () => {
                carousel.style.animationPlayState = 'paused';
            });
            
            carousel.addEventListener('mouseleave', () => {
                carousel.style.animationPlayState = 'running';
            });
        });
    }
    
    /**
     * Statistics counter animation
     */
    function initCounterAnimation() {
        const counters = document.querySelectorAll('.best-title');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.textContent.replace(/\D/g, ''));
                    const suffix = counter.textContent.replace(/\d/g, '');
                    
                    animateCounter(counter, 0, target, suffix, 2000);
                    observer.unobserve(counter);
                }
            });
        });
        
        counters.forEach(counter => observer.observe(counter));
    }
    
    function animateCounter(element, start, end, suffix, duration) {
        const startTime = performance.now();
        
        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * easeOutQuart(progress));
            element.textContent = current + suffix;
            
            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }
        
        requestAnimationFrame(updateCounter);
    }
    
    function easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }
    
    // Initialize additional components
    initLogoCarousel();
    initCounterAnimation();
    
    /**
     * Parallax effect for hero section
     */
    function initParallaxEffect() {
        const hero = document.querySelector('.hero-section');
        if (!hero) return;
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Initialize parallax (optional - can be resource intensive)
    // initParallaxEffect();
    
    /**
     * Mobile menu toggle (if needed)
     */
    function initMobileMenu() {
        const menuToggle = document.getElementById('mobile-toggle');
        const mobileMenu = document.getElementById('mobile-nav');

        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                menuToggle.classList.toggle('active');

                // Prevent body scroll when menu is open
                if (mobileMenu.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            });

            // Close menu when clicking on links
            const mobileLinks = mobileMenu.querySelectorAll('.mobile-nav-link');
            mobileLinks.forEach(link => {
                link.addEventListener('click', () => {
                    mobileMenu.classList.remove('active');
                    menuToggle.classList.remove('active');
                    document.body.style.overflow = '';
                });
            });
        }
    }
    
    initMobileMenu();
    
    // Add loading class to body when page is fully loaded
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');
    });
    
    // Service Image Switcher
    function initializeServiceSwitcher() {
        const serviceCards = document.querySelectorAll('.service-mini-card');
        const serviceImage = document.getElementById('service-image');

        // Define service images with full paths
        const serviceImages = {
            'architectural': 'assets/images/demo-image/demo-images/imgi_18_6784f188034623b1f2de81a2_service.jpg',
            'structural': 'assets/images/demo-image/demo-images/imgi_40_678b4fa8626623b854fc160e_project-main01-p-800.jpg',
            'construction': 'assets/images/demo-image/demo-images/imgi_9_678b510a26ec52b2b74c5dc8_project-thumb02.jpg',
            'sustainable': 'assets/images/demo-image/demo-images/imgi_10_678b51888f09cbd817b6271f_project-thumb06-1.jpg'
        };

        const serviceAlts = {
            'architectural': 'Architectural Design',
            'structural': 'Structural Engineering',
            'construction': 'Construction Management',
            'sustainable': 'Sustainable Design'
        };

        serviceCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                serviceCards.forEach(c => c.classList.remove('active'));

                // Add active class to clicked card
                this.classList.add('active');

                // Get service type
                const serviceType = this.getAttribute('data-service');

                // Update image if service type exists
                if (serviceImages[serviceType] && serviceImage) {
                    serviceImage.src = serviceImages[serviceType];
                    serviceImage.alt = serviceAlts[serviceType];
                }
            });
        });
    }

    // Initialize service switcher
    initializeServiceSwitcher();

    /**
     * Enhanced image loading and fitting
     */
    function initImageLoading() {
        const coverImages = document.querySelectorAll('.cover-image');
        
        coverImages.forEach(img => {
            // Add loading class
            img.classList.add('loading');
            
            // Handle successful load
            img.addEventListener('load', function() {
                this.classList.remove('loading');
                this.classList.add('loaded');
                
                // Ensure proper object fit
                const container = this.closest('.work-main, .work-cover');
                if (container) {
                    this.style.objectFit = 'cover';
                    this.style.objectPosition = 'center';
                    this.style.width = '100%';
                    this.style.height = '100%';
                }
            });
            
            // Handle error
            img.addEventListener('error', function() {
                this.classList.remove('loading');
                this.classList.add('error');
                console.warn('Failed to load image:', this.src);
                
                // Set a placeholder background
                const container = this.closest('.work-main, .work-cover');
                if (container) {
                    container.style.backgroundColor = '#f5f5f5';
                    container.style.backgroundImage = 'linear-gradient(45deg, #f0f0f0 25%, transparent 25%), linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #f0f0f0 75%), linear-gradient(-45deg, transparent 75%, #f0f0f0 75%)';
                    container.style.backgroundSize = '20px 20px';
                    container.style.backgroundPosition = '0 0, 0 10px, 10px -10px, -10px 0px';
                }
            });
            
            // If image is already loaded (cached)
            if (img.complete && img.naturalHeight !== 0) {
                img.dispatchEvent(new Event('load'));
            }
        });
        
        // Lazy loading for better performance
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        if (img.dataset.src) {
                            img.src = img.dataset.src;
                            img.removeAttribute('data-src');
                            observer.unobserve(img);
                        }
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    // Initialize image loading
    initImageLoading();

    // Handle resize events
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            // Recalculate any size-dependent elements
            console.log('Window resized, recalculating layouts...');
        }, 250);
    });
    
    /**
     * Service Offcanvas Menu
     */
    function initServiceMenu() {
        // Create global functions for menu control
        window.toggleServiceMenu = function(event) {
            event.preventDefault();
            const offcanvas = document.getElementById('service-offcanvas');
            if (offcanvas) {
                offcanvas.classList.toggle('active');
                
                // Prevent body scroll when menu is open
                if (offcanvas.classList.contains('active')) {
                    document.body.style.overflow = 'hidden';
                } else {
                    document.body.style.overflow = '';
                }
            }
        };

        window.closeServiceMenu = function() {
            const offcanvas = document.getElementById('service-offcanvas');
            if (offcanvas) {
                offcanvas.classList.remove('active');
                document.body.style.overflow = '';
            }
        };

        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeServiceMenu();
            }
        });

        // Close menu when clicking on service items
        const serviceMenuItems = document.querySelectorAll('.service-menu-item');
        serviceMenuItems.forEach(item => {
            item.addEventListener('click', function() {
                setTimeout(closeServiceMenu, 100); // Small delay for better UX
            });
        });
    }

    // Initialize service menu
    initServiceMenu();
});
