/**
 * Enhanced Slider System for Monolith Design Co.
 * Handles animations, text breaking, and responsive behavior
 * Version: 2.0
 */

class EnhancedSlider {
    constructor(container) {
        this.container = container;
        this.slides = [];
        this.currentIndex = 0;
        this.isAnimating = false;
        this.autoPlayTimer = null;
        this.settings = {
            autoPlay: true,
            pauseOnHover: true,
            animationDuration: 5000,
            transitionSpeed: 800
        };
        
        this.init();
    }
    
    init() {
        this.collectSlideData();
        this.processTextBreaking();
        this.setupEventListeners();
        this.startAutoPlay();
        this.showSlide(0);
    }
    
    /**
     * Collect all slide data from DOM elements
     */
    collectSlideData() {
        const backgroundImages = this.container.querySelectorAll('.hero-background img');
        const textSlides = this.container.querySelectorAll('.hero-text-slide');
        
        this.slides = [];
        
        for (let i = 0; i < Math.min(backgroundImages.length, textSlides.length); i++) {
            const bgImg = backgroundImages[i];
            const textSlide = textSlides[i];
            
            this.slides.push({
                index: i,
                backgroundImage: bgImg,
                textSlide: textSlide,
                animationType: bgImg.dataset.animationType || 'fade',
                animationDuration: parseInt(bgImg.dataset.animationDuration) * 1000 || 5000,
                overlayColor: bgImg.dataset.overlayColor || '#000000',
                overlayOpacity: parseFloat(bgImg.dataset.overlayOpacity) || 0.5
            });
        }
        
        // Update settings from first slide
        if (this.slides.length > 0) {
            this.settings.animationDuration = this.slides[0].animationDuration;
        }
    }
    
    /**
     * Process text breaking for all slides
     */
    processTextBreaking() {
        this.slides.forEach(slide => {
            const titleElement = slide.textSlide.querySelector('.hero-title');
            const subtitleElement = slide.textSlide.querySelector('.hero-subtitle, .hero-info');
            
            if (titleElement) {
                this.applyTextBreaking(titleElement);
            }
            
            if (subtitleElement) {
                this.applyTextBreaking(subtitleElement);
            }
        });
    }
    
    /**
     * Apply text breaking based on data attributes
     */
    applyTextBreaking(element) {
        const breakType = element.dataset.breakType || 'none';
        const breakLimit = parseInt(element.dataset.breakLimit) || 50;
        const originalText = element.textContent.trim();
        
        if (breakType === 'none') {
            return;
        }
        
        let processedText = '';
        
        switch (breakType) {
            case 'character':
                processedText = this.breakByCharacters(originalText, breakLimit);
                break;
            case 'word':
                processedText = this.breakByWords(originalText, breakLimit);
                break;
            case 'manual':
                // For manual breaks, look for | or \n in the original text
                processedText = originalText.replace(/\|/g, '<br>').replace(/\\n/g, '<br>');
                break;
        }
        
        if (processedText) {
            element.innerHTML = processedText;
        }
    }
    
    /**
     * Break text by character count
     */
    breakByCharacters(text, limit) {
        if (text.length <= limit) {
            return text;
        }
        
        const words = text.split(' ');
        let lines = [];
        let currentLine = '';
        
        words.forEach(word => {
            if ((currentLine + word).length <= limit) {
                currentLine += (currentLine ? ' ' : '') + word;
            } else {
                if (currentLine) {
                    lines.push(currentLine);
                }
                currentLine = word;
            }
        });
        
        if (currentLine) {
            lines.push(currentLine);
        }
        
        return lines.join('<br>');
    }
    
    /**
     * Break text by word count
     */
    breakByWords(text, limit) {
        const words = text.split(' ');
        if (words.length <= limit) {
            return text;
        }
        
        const lines = [];
        for (let i = 0; i < words.length; i += limit) {
            lines.push(words.slice(i, i + limit).join(' '));
        }
        
        return lines.join('<br>');
    }
    
    /**
     * Show specific slide with animation
     */
    showSlide(index, direction = 'next') {
        if (this.isAnimating || index === this.currentIndex) {
            return;
        }
        
        this.isAnimating = true;
        
        const currentSlide = this.slides[this.currentIndex];
        const nextSlide = this.slides[index];
        
        if (!nextSlide) {
            this.isAnimating = false;
            return;
        }
        
        // Update overlay
        this.updateOverlay(nextSlide);
        
        // Animate background images
        this.animateBackground(currentSlide, nextSlide, direction);
        
        // Animate text content
        this.animateText(currentSlide, nextSlide, direction);
        
        // Update navigation dots
        this.updateDots(index);
        
        this.currentIndex = index;
        
        // Reset animation flag after transition
        setTimeout(() => {
            this.isAnimating = false;
        }, this.settings.transitionSpeed);
    }
    
    /**
     * Update background overlay
     */
    updateOverlay(slide) {
        const overlay = this.container.querySelector('.hero-overlay');
        if (overlay) {
            overlay.style.backgroundColor = slide.overlayColor;
            overlay.style.opacity = slide.overlayOpacity;
        }
    }
    
    /**
     * Animate background images
     */
    animateBackground(currentSlide, nextSlide, direction) {
        // Remove active class from current background
        if (currentSlide) {
            currentSlide.backgroundImage.classList.remove('active');
        }
        
        // Add active class to next background
        nextSlide.backgroundImage.classList.add('active');
        
        // Apply animation based on type
        this.applyBackgroundAnimation(nextSlide);
    }
    
    /**
     * Apply specific background animation
     */
    applyBackgroundAnimation(slide) {
        const img = slide.backgroundImage;
        const animationType = slide.animationType;
        
        // Remove any existing animation classes
        img.classList.remove('fade-in', 'slide-in', 'zoom-in', 'bounce-in');
        
        // Force reflow
        img.offsetHeight;
        
        // Add appropriate animation class
        switch (animationType) {
            case 'slide':
                img.classList.add('slide-in');
                break;
            case 'zoom':
                img.classList.add('zoom-in');
                break;
            case 'bounce':
                img.classList.add('bounce-in');
                break;
            default:
                img.classList.add('fade-in');
        }
    }
    
    /**
     * Animate text content
     */
    animateText(currentSlide, nextSlide, direction) {
        // Hide current text slide
        if (currentSlide) {
            currentSlide.textSlide.classList.remove('active');
        }
        
        // Show next text slide with delay for smooth transition
        setTimeout(() => {
            nextSlide.textSlide.classList.add('active');
        }, 200);
    }
    
    /**
     * Update navigation dots
     */
    updateDots(index) {
        const dots = this.container.querySelectorAll('.slide-dot, .dot');
        dots.forEach((dot, i) => {
            if (i === index) {
                dot.classList.add('active');
            } else {
                dot.classList.remove('active');
            }
        });
    }
    
    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Navigation dots
        const dots = this.container.querySelectorAll('.slide-dot, .dot');
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                this.showSlide(index);
                this.restartAutoPlay();
            });
        });
        
        // Arrow navigation
        const prevBtn = this.container.querySelector('.left-arrow, .slider-prev');
        const nextBtn = this.container.querySelector('.right-arrow, .slider-next');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', () => {
                this.previousSlide();
                this.restartAutoPlay();
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', () => {
                this.nextSlide();
                this.restartAutoPlay();
            });
        }
        
        // Pause on hover
        if (this.settings.pauseOnHover) {
            this.container.addEventListener('mouseenter', () => {
                this.stopAutoPlay();
            });
            
            this.container.addEventListener('mouseleave', () => {
                this.startAutoPlay();
            });
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                this.previousSlide();
                this.restartAutoPlay();
            } else if (e.key === 'ArrowRight') {
                this.nextSlide();
                this.restartAutoPlay();
            }
        });
    }
    
    /**
     * Go to next slide
     */
    nextSlide() {
        const nextIndex = (this.currentIndex + 1) % this.slides.length;
        this.showSlide(nextIndex, 'next');
    }
    
    /**
     * Go to previous slide
     */
    previousSlide() {
        const prevIndex = (this.currentIndex - 1 + this.slides.length) % this.slides.length;
        this.showSlide(prevIndex, 'prev');
    }
    
    /**
     * Start auto play
     */
    startAutoPlay() {
        if (!this.settings.autoPlay || this.slides.length <= 1) {
            return;
        }
        
        this.stopAutoPlay();
        this.autoPlayTimer = setInterval(() => {
            this.nextSlide();
        }, this.settings.animationDuration);
    }
    
    /**
     * Stop auto play
     */
    stopAutoPlay() {
        if (this.autoPlayTimer) {
            clearInterval(this.autoPlayTimer);
            this.autoPlayTimer = null;
        }
    }
    
    /**
     * Restart auto play
     */
    restartAutoPlay() {
        this.stopAutoPlay();
        setTimeout(() => {
            this.startAutoPlay();
        }, 2000);
    }
    
    /**
     * Destroy slider instance
     */
    destroy() {
        this.stopAutoPlay();
        // Remove event listeners and clean up
    }
}

// Initialize slider when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        window.monolithSlider = new EnhancedSlider(heroSection);
    }
});
