<?php
/**
 * Email Settings - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';
require_once '../includes/Database.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$message = '';
$messageType = '';

// Handle form submission
if ($_POST && isset($_POST['action'])) {
    switch ($_POST['action']) {
        case 'update_email_settings':
            try {
                updateThemeOption('smtp_enabled', isset($_POST['smtp_enabled']) ? '1' : '0');
                updateThemeOption('smtp_host', sanitizeInput($_POST['smtp_host']));
                updateThemeOption('smtp_port', sanitizeInput($_POST['smtp_port']));
                updateThemeOption('smtp_username', sanitizeInput($_POST['smtp_username']));
                updateThemeOption('smtp_password', sanitizeInput($_POST['smtp_password']));
                updateThemeOption('smtp_encryption', sanitizeInput($_POST['smtp_encryption']));
                updateThemeOption('from_email', sanitizeInput($_POST['from_email']));
                updateThemeOption('from_name', sanitizeInput($_POST['from_name']));
                updateThemeOption('admin_email', sanitizeInput($_POST['admin_email']));
                
                $message = 'Email settings updated successfully!';
                $messageType = 'success';
            } catch (Exception $e) {
                $message = 'Error updating email settings: ' . $e->getMessage();
                $messageType = 'error';
            }
            break;
            
        case 'test_email':
            $testEmail = sanitizeInput($_POST['test_email']);
            if (validateEmail($testEmail)) {
                $testData = [
                    'name' => 'Test User',
                    'email' => $testEmail,
                    'phone' => 'Test Phone',
                    'service' => 'Test Service',
                    'message' => 'This is a test email to verify your email configuration is working properly.'
                ];
                
                $template = getEmailTemplate('contact_notification', $testData);
                if ($template) {
                    $sent = sendEmail($testEmail, $template['subject'], $template['body']);
                    if ($sent) {
                        $message = 'Test email sent successfully to ' . $testEmail;
                        $messageType = 'success';
                    } else {
                        $message = 'Failed to send test email. Please check your email configuration.';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'Error generating email template.';
                    $messageType = 'error';
                }
            } else {
                $message = 'Please enter a valid email address for testing.';
                $messageType = 'error';
            }
            break;
    }
}

// Get current settings
$settings = [
    'smtp_enabled' => getThemeOption('smtp_enabled', '0'),
    'smtp_host' => getThemeOption('smtp_host', 'smtp.gmail.com'),
    'smtp_port' => getThemeOption('smtp_port', '587'),
    'smtp_username' => getThemeOption('smtp_username', ''),
    'smtp_password' => getThemeOption('smtp_password', ''),
    'smtp_encryption' => getThemeOption('smtp_encryption', 'tls'),
    'from_email' => getThemeOption('from_email', '<EMAIL>'),
    'from_name' => getThemeOption('from_name', 'Monolith Design Co.'),
    'admin_email' => getThemeOption('admin_email', ADMIN_EMAIL)
];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Settings - Admin Dashboard</title>
    <link rel="stylesheet" href="css/admin-style.css">
    <style>
        .email-settings {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .settings-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        
        .form-group {
            margin-bottom: 1rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
        }
        
        .btn {
            background: #E67E22;
            color: white;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 1rem;
        }
        
        .btn:hover {
            background: #d35400;
        }
        
        .btn-secondary {
            background: #95a5a6;
        }
        
        .btn-secondary:hover {
            background: #7f8c8d;
        }
        
        .message {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 0.25rem;
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Email Settings</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php">Contact Submissions</a>
        <a href="email-settings.php" class="active">Email Settings</a>
    </nav>
    
    <div class="admin-content">
        <div class="email-settings">
            <?php if ($message): ?>
                <div class="message <?php echo $messageType; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="settings-section">
                <h2>Email Configuration</h2>
                <p>Configure email settings for contact form notifications and system emails.</p>
                
                <form method="POST" action="">
                    <input type="hidden" name="action" value="update_email_settings">
                    
                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="smtp_enabled" name="smtp_enabled" <?php echo $settings['smtp_enabled'] ? 'checked' : ''; ?>>
                            <label for="smtp_enabled">Enable SMTP (recommended for production)</label>
                        </div>
                        <div class="help-text">If disabled, will use PHP's built-in mail() function</div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="smtp_host">SMTP Host</label>
                            <input type="text" id="smtp_host" name="smtp_host" value="<?php echo htmlspecialchars($settings['smtp_host']); ?>" placeholder="smtp.gmail.com">
                            <div class="help-text">Your email provider's SMTP server</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="smtp_port">SMTP Port</label>
                            <input type="number" id="smtp_port" name="smtp_port" value="<?php echo htmlspecialchars($settings['smtp_port']); ?>" placeholder="587">
                            <div class="help-text">587 for TLS, 465 for SSL</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="smtp_username">SMTP Username</label>
                            <input type="email" id="smtp_username" name="smtp_username" value="<?php echo htmlspecialchars($settings['smtp_username']); ?>" placeholder="<EMAIL>">
                        </div>
                        
                        <div class="form-group">
                            <label for="smtp_password">SMTP Password</label>
                            <input type="password" id="smtp_password" name="smtp_password" value="<?php echo htmlspecialchars($settings['smtp_password']); ?>" placeholder="Your email password">
                            <div class="help-text">Use app password for Gmail</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="smtp_encryption">Encryption</label>
                            <select id="smtp_encryption" name="smtp_encryption">
                                <option value="tls" <?php echo $settings['smtp_encryption'] === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                <option value="ssl" <?php echo $settings['smtp_encryption'] === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="admin_email">Admin Email</label>
                            <input type="email" id="admin_email" name="admin_email" value="<?php echo htmlspecialchars($settings['admin_email']); ?>" placeholder="<EMAIL>">
                            <div class="help-text">Where contact form notifications are sent</div>
                        </div>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="from_email">From Email</label>
                            <input type="email" id="from_email" name="from_email" value="<?php echo htmlspecialchars($settings['from_email']); ?>" placeholder="<EMAIL>">
                            <div class="help-text">Email address used as sender</div>
                        </div>
                        
                        <div class="form-group">
                            <label for="from_name">From Name</label>
                            <input type="text" id="from_name" name="from_name" value="<?php echo htmlspecialchars($settings['from_name']); ?>" placeholder="Monolith Design Co.">
                            <div class="help-text">Name displayed as sender</div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn">Save Email Settings</button>
                </form>
            </div>
            
            <div class="settings-section">
                <h2>Test Email Configuration</h2>
                <p>Send a test email to verify your configuration is working properly.</p>
                
                <form method="POST" action="">
                    <input type="hidden" name="action" value="test_email">
                    
                    <div class="form-group">
                        <label for="test_email">Test Email Address</label>
                        <input type="email" id="test_email" name="test_email" placeholder="<EMAIL>" required>
                        <div class="help-text">Enter an email address to receive the test email</div>
                    </div>
                    
                    <button type="submit" class="btn btn-secondary">Send Test Email</button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
