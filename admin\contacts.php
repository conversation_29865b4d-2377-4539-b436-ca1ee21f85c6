<?php
/**
 * Contact Submissions - Admin Panel
 */

define('MONOLITH_ACCESS', true);
require_once '../config.php';
require_once '../includes/functions.php';

// Simple authentication
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit;
}

// Handle actions
$message = '';
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                if (isset($_POST['submission_id'])) {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("UPDATE contact_submissions SET status = 'read' WHERE id = ?");
                    $stmt->execute([$_POST['submission_id']]);
                    $message = 'Submission marked as read.';
                }
                break;
                
            case 'mark_replied':
                if (isset($_POST['submission_id'])) {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("UPDATE contact_submissions SET status = 'replied' WHERE id = ?");
                    $stmt->execute([$_POST['submission_id']]);
                    $message = 'Submission marked as replied.';
                }
                break;
                
            case 'delete':
                if (isset($_POST['submission_id'])) {
                    $db = Database::getConnection();
                    $stmt = $db->prepare("DELETE FROM contact_submissions WHERE id = ?");
                    $stmt->execute([$_POST['submission_id']]);
                    $message = 'Submission deleted.';
                }
                break;
        }
    }
}

// Get all contact submissions
$db = Database::getConnection();
$stmt = $db->prepare("SELECT * FROM contact_submissions ORDER BY created_at DESC");
$stmt->execute();
$submissions = $stmt->fetchAll();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Submissions - Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .admin-header {
            background: #1A1A1A;
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .admin-header h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .admin-nav {
            background: white;
            padding: 0;
            display: flex;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .admin-nav a {
            padding: 1rem 1.5rem;
            text-decoration: none;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .admin-nav a:hover,
        .admin-nav a.active {
            color: #E67E22;
            border-bottom-color: #E67E22;
        }
        
        .admin-content {
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 2rem;
            border: 1px solid #c3e6cb;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #E67E22;
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .submissions-table {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 1.5rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .table-header h2 {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1A1A1A;
        }
        
        .table-container {
            overflow-x: auto;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-new {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-read {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-replied {
            background: #d4edda;
            color: #155724;
        }
        
        .action-buttons {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.375rem 0.75rem;
            border: none;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-link {
            background: none;
            border: none;
            color: #E67E22;
            text-decoration: underline;
            cursor: pointer;
            padding: 0;
            font-size: inherit;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .btn-link:hover {
            transform: none;
            box-shadow: none;
            color: #d35400;
        }
        
        .no-submissions {
            text-align: center;
            padding: 3rem;
            color: #666;
        }
        
        .no-submissions svg {
            width: 64px;
            height: 64px;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        @media (max-width: 768px) {
            .admin-content {
                padding: 1rem;
            }
            
            .admin-nav {
                overflow-x: auto;
            }
            
            .table-container {
                font-size: 0.875rem;
            }
            
            th, td {
                padding: 0.75rem 0.5rem;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>Contact Submissions</h1>
        <div>
            <a href="<?php echo siteUrl(); ?>" target="_blank" style="color: #E67E22; text-decoration: none;">View Website</a>
            <span style="margin: 0 1rem;">|</span>
            <a href="logout.php" style="color: #e74c3c; text-decoration: none;">Logout</a>
        </div>
    </div>
    
    <nav class="admin-nav">
        <a href="index.php">Theme Options</a>
        <a href="sliders.php">Sliders</a>
        <a href="hero-sections.php">Hero Sections</a>
        <a href="services.php">Services</a>
        <a href="projects.php">Projects</a>
        <a href="team.php">Team</a>
        <a href="testimonials.php">Testimonials</a>
        <a href="blog.php">Blog</a>
        <a href="contacts.php" class="active">Contact Submissions</a>
    </nav>
    
    <div class="admin-content">
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?php echo count($submissions); ?></div>
                <div class="stat-label">Total Submissions</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count(array_filter($submissions, function($s) { return $s['status'] === 'new'; })); ?></div>
                <div class="stat-label">New</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count(array_filter($submissions, function($s) { return $s['status'] === 'read'; })); ?></div>
                <div class="stat-label">Read</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo count(array_filter($submissions, function($s) { return $s['status'] === 'replied'; })); ?></div>
                <div class="stat-label">Replied</div>
            </div>
        </div>
        
        <!-- Submissions Table -->
        <div class="submissions-table">
            <div class="table-header">
                <h2>All Contact Submissions</h2>
            </div>
            
            <?php if (empty($submissions)): ?>
                <div class="no-submissions">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8v4a2 2 0 01-2 2H9a2 2 0 01-2-2V5a2 2 0 012-2h6a2 2 0 012 2z"/>
                    </svg>
                    <h3>No contact submissions yet</h3>
                    <p>Contact form submissions will appear here once people start contacting you.</p>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone</th>
                                <th>Service</th>
                                <th>Message</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($submissions as $submission): ?>
                                <tr>
                                    <td><strong><?php echo htmlspecialchars($submission['name']); ?></strong></td>
                                    <td>
                                        <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>" style="color: #E67E22; text-decoration: none;">
                                            <?php echo htmlspecialchars($submission['email']); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if (!empty($submission['phone'])): ?>
                                            <a href="tel:<?php echo htmlspecialchars($submission['phone']); ?>" style="color: #E67E22; text-decoration: none;">
                                                <?php echo htmlspecialchars($submission['phone']); ?>
                                            </a>
                                        <?php else: ?>
                                            <span style="color: #999;">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo !empty($submission['service']) ? htmlspecialchars($submission['service']) : '<span style="color: #999;">-</span>'; ?></td>
                                    <td style="max-width: 200px;">
                                        <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                                            <?php echo htmlspecialchars(substr($submission['message'], 0, 100)); ?>
                                            <?php if (strlen($submission['message']) > 100): ?>
                                                ...
                                                <button type="button" class="btn btn-link" onclick="showFullMessage(<?php echo $submission['id']; ?>)" style="padding: 0; margin-left: 5px; color: #E67E22; text-decoration: underline; background: none; border: none; cursor: pointer;">
                                                    View Full
                                                </button>
                                            <?php endif; ?>
                                        </div>

                                        <!-- Hidden full details for modal -->
                                        <div id="full-details-<?php echo $submission['id']; ?>" style="display: none;">
                                            <div style="margin-bottom: 15px;">
                                                <strong>Name:</strong> <?php echo htmlspecialchars($submission['name']); ?>
                                            </div>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Email:</strong> <a href="mailto:<?php echo htmlspecialchars($submission['email']); ?>" style="color: #E67E22;"><?php echo htmlspecialchars($submission['email']); ?></a>
                                            </div>
                                            <?php if (!empty($submission['phone'])): ?>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Phone:</strong> <a href="tel:<?php echo htmlspecialchars($submission['phone']); ?>" style="color: #E67E22;"><?php echo htmlspecialchars($submission['phone']); ?></a>
                                            </div>
                                            <?php endif; ?>
                                            <?php if (!empty($submission['service'])): ?>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Service:</strong> <?php echo htmlspecialchars($submission['service']); ?>
                                            </div>
                                            <?php endif; ?>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Status:</strong> <span class="status-badge status-<?php echo $submission['status']; ?>"><?php echo ucfirst($submission['status']); ?></span>
                                            </div>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Submitted:</strong> <?php echo date('F j, Y \a\t g:i A', strtotime($submission['created_at'])); ?>
                                            </div>
                                            <div style="margin-bottom: 15px;">
                                                <strong>Message:</strong>
                                            </div>
                                            <div style="background: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid #E67E22; white-space: pre-wrap; word-wrap: break-word;">
                                                <?php echo htmlspecialchars($submission['message']); ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $submission['status']; ?>">
                                            <?php echo ucfirst($submission['status']); ?>
                                        </span>
                                    </td>
                                    <td><?php echo date('M j, Y g:i A', strtotime($submission['created_at'])); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button type="button" class="btn btn-info" onclick="showFullMessage(<?php echo $submission['id']; ?>)">View Details</button>

                                            <?php if ($submission['status'] === 'new'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="mark_read">
                                                    <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                                    <button type="submit" class="btn btn-primary">Mark Read</button>
                                                </form>
                                            <?php endif; ?>

                                            <?php if ($submission['status'] !== 'replied'): ?>
                                                <form method="POST" style="display: inline;">
                                                    <input type="hidden" name="action" value="mark_replied">
                                                    <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                                    <button type="submit" class="btn btn-success">Mark Replied</button>
                                                </form>
                                            <?php endif; ?>

                                            <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this submission?')">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="submission_id" value="<?php echo $submission['id']; ?>">
                                                <button type="submit" class="btn btn-danger">Delete</button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Message Modal -->
    <div id="messageModal" style="display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5);">
        <div style="background-color: white; margin: 5% auto; padding: 20px; border-radius: 8px; width: 80%; max-width: 600px; max-height: 80%; overflow-y: auto;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                <h3 style="margin: 0; color: #333;">Contact Submission Details</h3>
                <button onclick="closeModal()" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #999;">&times;</button>
            </div>
            <div id="modalContent" style="line-height: 1.6; color: #333; white-space: pre-wrap; word-wrap: break-word;">
                <!-- Message content will be inserted here -->
            </div>
            <div style="margin-top: 20px; text-align: right; border-top: 1px solid #eee; padding-top: 15px;">
                <button onclick="closeModal()" style="background: #E67E22; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">Close</button>
            </div>
        </div>
    </div>

    <script>
        function showFullMessage(submissionId) {
            const fullDetailsDiv = document.getElementById('full-details-' + submissionId);
            const modal = document.getElementById('messageModal');
            const modalContent = document.getElementById('modalContent');

            if (fullDetailsDiv && modal && modalContent) {
                modalContent.innerHTML = fullDetailsDiv.innerHTML;
                modal.style.display = 'block';

                // Prevent body scroll when modal is open
                document.body.style.overflow = 'hidden';
            }
        }

        function closeModal() {
            const modal = document.getElementById('messageModal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = 'auto';
            }
        }

        // Close modal when clicking outside of it
        window.onclick = function(event) {
            const modal = document.getElementById('messageModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // Close modal with Escape key
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>
