<?php
/**
 * Monolith Design Co. - Homepage (Arkify-inspired Design)
 */

define('MONOLITH_ACCESS', true);
require_once 'config.php';
require_once 'includes/functions.php';

// Get homepage data
$services = getServices(4); // Get first 4 services for homepage
$featured_projects = getProjects(null, 4); // Get 4 featured projects
$testimonials = getTestimonials(8); // Get more testimonials for slider

// Get sliders from database
try {
    $sliders = getSliders();
} catch (Exception $e) {
    $sliders = [];
}

// Get latest blog posts for Latest Articles section
try {
    $db = Database::getConnection();
    $stmt = $db->query("SELECT * FROM blog_posts WHERE status = 'published' AND active = 1 ORDER BY published_at DESC LIMIT 4");
    $blog_posts = $stmt->fetchAll();
} catch (Exception $e) {
    $blog_posts = [];
}

// Page metadata
$page_title = 'Innovative Architecture by ' . SITE_NAME;
$page_description = 'We are the forefront of special design, in a place where we help you visualise your project through the use of 3D imagery and motion.';
$page_keywords = 'innovative architecture, architectural design, 3D visualization, modern design, construction, engineering';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <meta name="description" content="<?php echo $page_description; ?>">
    <meta name="keywords" content="<?php echo $page_keywords; ?>">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo themeUrl('images/favicon.ico'); ?>">

    <!-- Google Fonts - Arkify uses Public Sans and Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- CSS -->
    <link rel="stylesheet" href="<?php echo themeUrl('css/arkify-style.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/work-section-clean.css'); ?>">
    <link rel="stylesheet" href="<?php echo themeUrl('css/responsive.css'); ?>">

    <!-- Custom accent color -->
    <style>
        :root {
            --accent-color: <?php echo getThemeOption('accent_color', '#2D5A27'); ?>;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <?php loadTemplate('header'); ?>

    <!-- Main Content -->
    <main>
        <!-- Hero Section - Arkify Style -->
        <section class="hero-section loading" id="hero">
            <!-- Background Images -->
            <div class="hero-background">
                <?php if (!empty($sliders)): ?>
                    <?php foreach ($sliders as $index => $slider): ?>
                        <img src="<?php echo ensureAbsoluteUrl($slider['background_image']); ?>"
                             alt="<?php echo htmlspecialchars($slider['title']); ?>"
                             class="<?php echo $index === 0 ? 'active' : ''; ?>"
                             data-overlay-color="<?php echo $slider['overlay_color'] ?? '#000000'; ?>"
                             data-overlay-opacity="<?php echo $slider['overlay_opacity'] ?? 0.5; ?>"
                             data-animation-type="<?php echo $slider['animation_type'] ?? 'fade'; ?>"
                             data-animation-duration="<?php echo $slider['animation_duration'] ?? 5; ?>"
                             data-auto-play="<?php echo $slider['auto_play'] ?? 1; ?>"
                             data-pause-on-hover="<?php echo $slider['pause_on_hover'] ?? 1; ?>">
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Fallback images if no sliders in database -->
                    <img src="<?php echo themeUrl('images/demo-image/demo-images/slider101.jpg'); ?>" alt="Architecture Background 1" class="active">
                    <img src="<?php echo themeUrl('images/demo-image/demo-images/slider202.jpg'); ?>" alt="Architecture Background 2">
                    <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_40_678b4fa8626623b854fc160e_project-main01-p-800.jpg'); ?>" alt="Architecture Background 3">
                    <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_41_678b510a26ec52b2b74c5dc8_project-thumb02-p-800.jpg'); ?>" alt="Architecture Background 4">
                <?php endif; ?>
            </div>
            <!-- Overlay -->
            <div class="hero-overlay"></div>

            <div class="hero-outer">
                <div class="container">
                    <div class="hero-wrap">
                        <!-- Text Slides -->
                        <div class="hero-text-slider">
                            <?php if (!empty($sliders)): ?>
                                <?php foreach ($sliders as $index => $slider): ?>
                                    <div class="hero-text-slide <?php echo $index === 0 ? 'active' : ''; ?>"
                                         style="color: <?php echo $slider['text_color'] ?? '#ffffff'; ?>;"
                                         data-content-alignment="<?php echo $slider['content_alignment'] ?? 'center'; ?>"
                                         data-vertical-alignment="<?php echo $slider['vertical_alignment'] ?? 'center'; ?>">
                                        <h1 class="hero-title"
                                            data-char-limit="<?php echo $slider['title_char_limit'] ?? 50; ?>"
                                            data-break-type="<?php echo $slider['title_break_type'] ?? 'none'; ?>"
                                            data-break-limit="<?php echo $slider['title_break_limit'] ?? 50; ?>"
                                            style="
                                                font-size: <?php echo $slider['title_font_size'] ?? 3.5; ?>rem;
                                                font-family: <?php echo $slider['title_font_family'] ?? 'inherit'; ?>;
                                                font-weight: <?php echo $slider['title_font_weight'] ?? 700; ?>;
                                                text-transform: <?php echo $slider['title_text_transform'] ?? 'none'; ?>;
                                                line-height: <?php echo $slider['title_line_height'] ?? 1.2; ?>;
                                                word-wrap: break-word;
                                                overflow-wrap: break-word;
                                            ">
                                            <?php echo htmlspecialchars($slider['title']); ?>
                                        </h1>
                                        <div class="hero-info">
                                            <h5 class="hero-data"
                                                data-char-limit="<?php echo $slider['description_char_limit'] ?? 100; ?>"
                                                data-break-type="<?php echo $slider['description_break_type'] ?? 'none'; ?>"
                                                data-break-limit="<?php echo $slider['description_break_limit'] ?? 100; ?>"
                                                style="
                                                    font-size: <?php echo $slider['description_font_size'] ?? 1.25; ?>rem;
                                                    font-family: <?php echo $slider['description_font_family'] ?? 'inherit'; ?>;
                                                    font-weight: <?php echo $slider['description_font_weight'] ?? 400; ?>;
                                                    text-transform: <?php echo $slider['description_text_transform'] ?? 'none'; ?>;
                                                    line-height: <?php echo $slider['description_line_height'] ?? 1.6; ?>;
                                                    word-wrap: break-word;
                                                    overflow-wrap: break-word;
                                                ">
                                                <?php echo htmlspecialchars($slider['subtitle']); ?>
                                            </h5>
                                            <?php if ($slider['button_text'] && $slider['button_link']): ?>
                                                <a href="<?php echo siteUrl($slider['button_link']); ?>" class="hero-button">
                                                    <?php echo htmlspecialchars($slider['button_text']); ?>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <!-- Fallback slides if no sliders in database -->
                                <div class="hero-text-slide active">
                                    <h1 class="hero-title">Innovative Architecture by <?php echo SITE_NAME; ?></h1>
                                    <div class="hero-info">
                                        <h5 class="hero-data">We are the forefront of special design, in a place where we help you visualise your project through the use of 3D imagery and motion</h5>
                                        <a href="#about" class="hero-button">Explore</a>
                                    </div>
                                </div>
                                <div class="hero-text-slide">
                                    <h1 class="hero-title">Sustainable Design Solutions</h1>
                                    <div class="hero-info">
                                        <h5 class="hero-data">Creating environmentally conscious buildings that harmonize with nature while delivering exceptional functionality and aesthetic appeal</h5>
                                        <a href="<?php echo siteUrl('services'); ?>" class="hero-button">Our Services</a>
                                    </div>
                                </div>
                                <div class="hero-text-slide">
                                    <h1 class="hero-title">Award-Winning Projects</h1>
                                    <div class="hero-info">
                                        <h5 class="hero-data">From residential masterpieces to commercial landmarks, our portfolio showcases excellence in architectural innovation and craftsmanship</h5>
                                        <a href="<?php echo siteUrl('projects'); ?>" class="hero-button">View Projects</a>
                                    </div>
                                </div>
                                <div class="hero-text-slide">
                                    <h1 class="hero-title">Future-Ready Architecture</h1>
                                    <div class="hero-info">
                                        <h5 class="hero-data">Embracing cutting-edge technology and smart building solutions to create spaces that adapt to tomorrow's needs</h5>
                                        <a href="<?php echo siteUrl('contact'); ?>" class="hero-button">Get Started</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Slider Navigation -->
                        <?php
                        // Check if any slider has navigation dots enabled
                        $showDots = false;
                        foreach ($sliders as $slider) {
                            if (($slider['show_navigation_dots'] ?? 1) == 1) {
                                $showDots = true;
                                break;
                            }
                        }
                        ?>
                        <?php if ($showDots): ?>
                            <div class="hero-slider-nav">
                                <div class="slider-dots">
                                    <?php foreach ($sliders as $index => $slider): ?>
                                        <span class="dot <?php echo $index === 0 ? 'active' : ''; ?>" data-slide="<?php echo $index; ?>"></span>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </section>
        <!-- About Section - Arkify Style -->
        <section id="about" class="about-section">
            <div class="container">
                <div class="about-wrap">
                    <div class="about-title">
                        <div class="subtitle">
                            <div class="caption">ABOUT US</div>
                        </div>
                        <div class="about-cover">
                            <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_6_6784d779148e3dba714b407c_about-02.jpg'); ?>" alt="About Image" class="cover-image">
                        </div>
                    </div>
                    <div class="about-middle">
                        <div class="about-top">
                            <div class="about-info">
                                <h3>Our vision is to create thoughtful, efficient and sustainable transportation systems that improve the quality of life for people in urban environments.</h3>
                                <p>Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice, you can have confidence and clarity in your stakeholder communications.</p>
                            </div>
                        </div>
                        <div class="about-button-wrap">
                            <a href="<?php echo siteUrl('about'); ?>" class="primary-button">
                                <div>About Us</div>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="about-image">
                        <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_7_6784d66c8f77f815b7d5d889_about-01.jpg'); ?>" alt="About Image" class="cover-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Featured Projects Section - Arkify Style -->
        <section class="featured-projects-section">
            <div class="container">
                <div class="section-title">
                    <div class="subtitle">
                        <div>PROJECT</div>
                    </div>
                    <h2>Featured Work</h2>
                </div>

                <div class="work-outer-main">
                    <div class="work-wrapper">
                        <?php if (!empty($featured_projects)): ?>
                            <!-- Main featured project -->
                            <div class="work-main-card">
                                <div class="work-main">
                                    <img src="<?php echo ensureAbsoluteUrl($featured_projects[0]['featured_image']); ?>" alt="<?php echo htmlspecialchars($featured_projects[0]['title']); ?>" class="cover-image">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div><?php echo htmlspecialchars($featured_projects[0]['location'] ?? 'Location'); ?></div>
                                        <div class="card-outer">
                                            <div class="work-mini-card">
                                                <div class="body-small"><?php echo htmlspecialchars($featured_projects[0]['category']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6><?php echo htmlspecialchars($featured_projects[0]['title']); ?></h6>
                                    <?php if (!empty($featured_projects[0]['description'])): ?>
                                        <p class="work-excerpt"><?php echo htmlspecialchars(substr($featured_projects[0]['description'], 0, 120)) . '...'; ?></p>
                                    <?php endif; ?>
                                </div>
                                <a href="<?php echo siteUrl('project-details?slug=' . $featured_projects[0]['slug']); ?>" class="work-link"></a>
                            </div>

                            <!-- Secondary projects -->
                            <div class="work-list">
                                <?php for ($i = 1; $i < min(4, count($featured_projects)); $i++): ?>
                                    <div class="work-card">
                                        <div class="work-cover">
                                            <img src="<?php echo ensureAbsoluteUrl($featured_projects[$i]['featured_image']); ?>" alt="<?php echo htmlspecialchars($featured_projects[$i]['title']); ?>" class="cover-image">
                                        </div>
                                        <div class="work-content">
                                            <div class="work-title">
                                                <div><?php echo htmlspecialchars($featured_projects[$i]['location'] ?? 'Location'); ?></div>
                                                <div class="work-mini-card">
                                                    <div class="body-small"><?php echo htmlspecialchars($featured_projects[$i]['category']); ?></div>
                                                </div>
                                            </div>
                                            <h6><?php echo htmlspecialchars($featured_projects[$i]['title']); ?></h6>
                                            <?php if (!empty($featured_projects[$i]['description'])): ?>
                                                <p class="work-excerpt"><?php echo htmlspecialchars(substr($featured_projects[$i]['description'], 0, 80)) . '...'; ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <a href="<?php echo siteUrl('project-details?slug=' . $featured_projects[$i]['slug']); ?>" class="work-link"></a>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        <?php else: ?>
                            <!-- Default projects with background images -->
                            <div class="work-main-card">
                                <div class="work-main" style="background-image: url('https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=building');">
                                </div>
                                <div class="work-content">
                                    <div class="work-title">
                                        <div>San Francisco, CA</div>
                                        <div class="card-outer">
                                            <div class="work-mini-card">
                                                <div class="body-small">Commercial</div>
                                            </div>
                                        </div>
                                    </div>
                                    <h6>Modern Office Complex</h6>
                                </div>
                            </div>

                            <!-- Secondary projects -->
                            <div class="work-list">
                                <div class="work-card">
                                    <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=300&fit=crop&crop=building');">
                                    </div>
                                    <div class="work-content">
                                        <div class="work-title">
                                            <div>New York, NY</div>
                                            <div class="work-mini-card">
                                                <div class="body-small">Residential</div>
                                            </div>
                                        </div>
                                        <h6>Luxury Residential Tower</h6>
                                    </div>
                                </div>

                                <div class="work-card">
                                    <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1448630360428-65456885c650?w=400&h=300&fit=crop&crop=building');">
                                    </div>
                                    <div class="work-content">
                                        <div class="work-title">
                                            <div>Austin, TX</div>
                                            <div class="work-mini-card">
                                                <div class="body-small">Commercial</div>
                                            </div>
                                        </div>
                                        <h6>Innovation Center</h6>
                                    </div>
                                </div>

                                <div class="work-card">
                                    <div class="work-cover" style="background-image: url('https://images.unsplash.com/photo-1582407947304-fd86f028f716?w=400&h=300&fit=crop&crop=building');">
                                    </div>
                                    <div class="work-content">
                                        <div class="work-title">
                                            <div>Portland, OR</div>
                                            <div class="work-mini-card">
                                                <div class="body-small">Residential</div>
                                            </div>
                                        </div>
                                        <h6>Sustainable Housing Development</h6>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="work-last">
                        <p>Our strategic and technical advisory service isn't based on a hunch. It's backed by years of experience, extensive technical knowledge and data-driven insights. With our trusted advice,</p>
                        <a href="<?php echo siteUrl('projects'); ?>" class="primary-button">
                            <div>VIEW ALL</div>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Statistics Section - Arkify Style -->
        <section class="statistics-section bg-primary">
            <div class="container">
                <div class="best-wrap">
                    <div class="best-top">
                        <div class="subtitle-white">
                            <div>WHY WE ARE THE BEST</div>
                        </div>
                        <div class="best-inner">
                            <div class="best-card">
                                <div class="best-icon">
                                    <img src="<?php echo themeUrl('images/icons/excellence.svg'); ?>" alt="Experience Icon">
                                </div>
                                <h2 class="best-title">35</h2>
                                <div class="body-large">Years of Experience</div>
                            </div>
                            <div class="best-card">
                                <div class="best-icon">
                                    <img src="<?php echo themeUrl('images/icons/innovation.svg'); ?>" alt="Innovation Icon">
                                </div>
                                <h2 class="best-title">2K+</h2>
                                <div class="body-large">Innovation & Research</div>
                            </div>
                            <div class="best-card">
                                <div class="best-icon">
                                    <img src="<?php echo themeUrl('images/icons/construction.svg'); ?>" alt="Projects Icon">
                                </div>
                                <h2 class="best-title">210</h2>
                                <div class="body-large">Projects Completed</div>
                            </div>
                            <div class="best-card">
                                <div class="best-icon">
                                    <img src="<?php echo themeUrl('images/icons/integrity.svg'); ?>" alt="Success Icon">
                                </div>
                                <h1 class="best-title">98%</h1>
                                <div class="body-large">Project Success Rate</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </section>

        <!-- Services Section - Arkify Style -->
        <section class="services-section">
            <div class="container">
                <div class="service-block">
                    <div class="project-title">
                        <div class="section-title">
                            <div class="subtitle">
                                <div>Discover Our Services</div>
                            </div>
                            <h2>Transforming Concepts into Iconic Architectural Masterpieces Through Innovation and Expertise</h2>
                        </div>
                        <div class="service-left">
                            <?php if (!empty($services)): ?>
                                <?php
                                $service_types = ['architectural', 'structural', 'construction', 'sustainable'];
                                foreach ($services as $index => $service):
                                    $data_service = isset($service_types[$index]) ? $service_types[$index] : '';
                                    $active_class = $index === 0 ? ' active' : '';
                                ?>
                                    <div class="service-mini-card<?php echo $active_class; ?>" data-service="<?php echo $data_service; ?>">
                                        <h5><?php echo ($index + 1) . '. ' . htmlspecialchars($service['title']); ?></h5>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="service-mini-card active" data-service="architectural">
                                    <h5>1. Architectural Design</h5>
                                </div>
                                <div class="service-mini-card" data-service="structural">
                                    <h5>2. Structural Engineering</h5>
                                </div>
                                <div class="service-mini-card" data-service="construction">
                                    <h5>3. Construction Management</h5>
                                </div>
                                <div class="service-mini-card" data-service="sustainable">
                                    <h5>4. Sustainable Design</h5>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="service-image">
                        <img id="service-image" src="<?php echo themeUrl('images/demo-image/demo-images/imgi_18_6784f188034623b1f2de81a2_service.jpg'); ?>" alt="Architectural Design" class="cover-image">
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials Section - Arkify Style -->
        <section class="testimonials-section">
            <div class="container">
                <div class="testimonials-slider">
                    <?php if (!empty($testimonials)): ?>
                        <?php foreach ($testimonials as $index => $testimonial): ?>
                            <div class="testimonial-slide <?php echo $index === 0 ? 'active' : ''; ?>">
                                <div class="review-wrap">
                                    <div class="review-cover">
                                        <?php
                                        // Use background_image for the slide background, fallback to a default project image
                                        $background_img = $testimonial['background_image'] ?? 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?w=800&h=600&fit=crop';
                                        ?>
                                        <img src="<?php echo ensureAbsoluteUrl($background_img); ?>" alt="Project Background" class="cover-image">
                                    </div>
                                    <div class="review-inner">
                                        <p class="review-info"><?php echo htmlspecialchars($testimonial['quote']); ?></p>
                                        <div class="review-block">
                                            <div class="review-title">
                                                <h5><?php echo htmlspecialchars($testimonial['client_name']); ?></h5>
                                                <div><?php echo htmlspecialchars($testimonial['client_position'] ?? $testimonial['client_company']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <!-- Default testimonials -->
                        <div class="testimonial-slide active">
                            <div class="review-wrap">
                                <div class="review-cover">
                                    <img src="https://cdn.prod.website-files.com/67848bcfe80146dcb18cc8b8/678b7db1626623b854fc160e_testimonial-01.jpg" alt="Review Image" class="cover-image">
                                </div>
                                <div class="review-inner">
                                    <p class="review-info">Working with this team was one of the best decisions we made for our project. They understood our vision from the very first meeting and brought it to life in ways we couldn't have imagined. The attention to detail, the thoughtful selection of materials, and their innovative approach to design truly set them apart.</p>
                                    <div class="review-block">
                                        <div class="review-title">
                                            <h5>Brooklyn Simmons</h5>
                                            <div>Creative Director</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Slider Navigation -->
                    <div class="slider-navigation">
                        <div class="left-arrow">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="right-arrow">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="slide-nav">
                            <?php for ($i = 1; $i <= max(count($testimonials), 1); $i++): ?>
                                <div class="slide-dot <?php echo $i === 1 ? 'active' : ''; ?>"><?php echo $i; ?></div>
                            <?php endfor; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Blog/Articles Section - Arkify Style -->
        <section class="articles-section">
            <div class="container">
                <div class="section-wrap">
                    <div class="title-wrap">
                        <div class="section-title">
                            <div class="subtitle">
                                <div>Articles</div>
                            </div>
                            <h2>Latest Articles</h2>
                        </div>
                        <div class="view-all-button">
                            <a href="<?php echo siteUrl('blog'); ?>" class="primary-button">
                                <div>VIEW ALL</div>
                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M1 8H15M8 1L15 8L8 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <div class="articles-grid">
                        <?php if (!empty($blog_posts)): ?>
                            <!-- Main featured article -->
                            <div class="article-main-card">
                                <div class="article-block">
                                    <img src="<?php echo ensureAbsoluteUrl($blog_posts[0]['featured_image'] ?? themeUrl('images/demo-image/demo-images/imgi_29_678b4ecb2c8379ee925d5088_blog-thumb01.jpg')); ?>" alt="<?php echo htmlspecialchars($blog_posts[0]['title']); ?>" class="cover-image">
                                </div>
                                <div class="article-title">
                                    <div class="article-top">
                                        <div><?php echo htmlspecialchars($blog_posts[0]['category'] ?? 'General'); ?></div>
                                        <div class="article-line"></div>
                                        <div><?php echo date('F j, Y', strtotime($blog_posts[0]['published_at'] ?? $blog_posts[0]['created_at'])); ?></div>
                                    </div>
                                    <h4><?php echo htmlspecialchars($blog_posts[0]['title']); ?></h4>
                                </div>
                                <a href="<?php echo siteUrl('news-details?slug=' . $blog_posts[0]['slug']); ?>" class="article-link"></a>
                            </div>
                        <?php else: ?>
                            <!-- Fallback main featured article -->
                            <div class="article-main-card">
                                <div class="article-block">
                                    <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_29_678b4ecb2c8379ee925d5088_blog-thumb01.jpg'); ?>" alt="Featured Article" class="cover-image">
                                </div>
                                <div class="article-title">
                                    <div class="article-top">
                                        <div>Urban Design</div>
                                        <div class="article-line"></div>
                                        <div>January 20, 2025</div>
                                    </div>
                                    <h4>Redefining Urban Spaces: How Modern Architecture is Reshaping Sustainable Cityscapes</h4>
                                </div>
                                <a href="<?php echo siteUrl('news'); ?>" class="article-link"></a>
                            </div>
                        <?php endif; ?>

                        <!-- Secondary articles -->
                        <div class="article-wrap">
                            <?php if (!empty($blog_posts) && count($blog_posts) > 1): ?>
                                <?php for ($i = 1; $i < min(4, count($blog_posts)); $i++): ?>
                                    <div class="article-card">
                                        <div class="article-cover">
                                            <img src="<?php echo ensureAbsoluteUrl($blog_posts[$i]['featured_image'] ?? themeUrl('images/demo-image/demo-images/imgi_30_678b4f658af081131d20e2de_blog-thumb07.jpg')); ?>" alt="<?php echo htmlspecialchars($blog_posts[$i]['title']); ?>" class="cover-image">
                                        </div>
                                        <div class="article-title">
                                            <div class="article-top">
                                                <div><?php echo htmlspecialchars($blog_posts[$i]['category'] ?? 'General'); ?></div>
                                                <div class="article-line"></div>
                                                <div><?php echo date('F j, Y', strtotime($blog_posts[$i]['published_at'] ?? $blog_posts[$i]['created_at'])); ?></div>
                                            </div>
                                            <h5><?php echo htmlspecialchars($blog_posts[$i]['title']); ?></h5>
                                        </div>
                                        <a href="<?php echo siteUrl('news-details?slug=' . $blog_posts[$i]['slug']); ?>" class="article-link"></a>
                                    </div>
                                <?php endfor; ?>
                            <?php else: ?>
                                <!-- Fallback secondary articles -->
                                <div class="article-card">
                                    <div class="article-cover">
                                        <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_30_678b4f658af081131d20e2de_blog-thumb07.jpg'); ?>" alt="Article" class="cover-image">
                                    </div>
                                    <div class="article-title">
                                        <div class="article-top">
                                            <div>Psychology & Design</div>
                                            <div class="article-line"></div>
                                            <div>January 20, 2025</div>
                                        </div>
                                        <h5>The Psychology of Space: How Architecture Affects Our Emotions</h5>
                                    </div>
                                    <a href="<?php echo siteUrl('news'); ?>" class="article-link"></a>
                                </div>

                                <div class="article-card">
                                    <div class="article-cover">
                                        <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_31_678b4efcd2d5f6237112a615_blog-thumb04.jpg'); ?>" alt="Article" class="cover-image">
                                    </div>
                                    <div class="article-title">
                                        <div class="article-top">
                                            <div>Architectural Design</div>
                                            <div class="article-line"></div>
                                            <div>January 20, 2025</div>
                                        </div>
                                        <h5>From Vision to Reality: Behind the Scenes of Iconic Architectural Creations</h5>
                                    </div>
                                    <a href="<?php echo siteUrl('news'); ?>" class="article-link"></a>
                                </div>

                                <div class="article-card">
                                    <div class="article-cover">
                                        <img src="<?php echo themeUrl('images/demo-image/demo-images/imgi_32_678b4f7826ec52b2b74b9f9c_blog-thumb08.jpg'); ?>" alt="Article" class="cover-image">
                                    </div>
                                    <div class="article-title">
                                        <div class="article-top">
                                            <div>Design Trends</div>
                                            <div class="article-line"></div>
                                            <div>January 20, 2025</div>
                                        </div>
                                        <h5>The Rise of Biophilic Design in Modern Architecture</h5>
                                    </div>
                                    <a href="<?php echo siteUrl('news'); ?>" class="article-link"></a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Dynamic Hero CTA Section -->
        <?php
        // Set page name for hero section lookup
        $hero_page_name = 'home';
        // Load the dynamic hero CTA template
        include 'templates/hero-cta.php';
        ?>
    </main>

    <!-- Footer -->
    <?php loadTemplate('footer'); ?>

    <!-- JavaScript -->
    <script src="<?php echo themeUrl('js/arkify-main.js'); ?>"></script>

    <!-- Dynamic Slider Styles -->
    <style id="dynamic-slider-styles">
        <?php
        // Check if universal height is enabled
        $use_universal_height = getThemeOption('use_universal_slider_height', '1');
        $universal_height_desktop = getThemeOption('universal_slider_height_desktop', '600');
        $universal_height_tablet = getThemeOption('universal_slider_height_tablet', '450');
        $universal_height_mobile = getThemeOption('universal_slider_height_mobile', '350');
        ?>

        /* Reset any conflicting styles */
        .hero-section {
            padding: 0 !important;
            margin: 0 !important;
        }

        <?php if ($use_universal_height === '1'): ?>
            /* Universal Height Settings */
            .hero-section {
                height: <?php echo $universal_height_desktop; ?>px !important;
                min-height: <?php echo $universal_height_desktop; ?>px !important;
                padding-top: 90px !important;
                padding-bottom: 40px !important;
            }

            @media (max-width: 992px) {
                .hero-section {
                    height: <?php echo $universal_height_tablet; ?>px !important;
                    min-height: <?php echo $universal_height_tablet; ?>px !important;
                }
            }

            @media (max-width: 768px) {
                .hero-section {
                    height: <?php echo $universal_height_mobile; ?>px !important;
                    min-height: <?php echo $universal_height_mobile; ?>px !important;
                }
            }
        <?php else: ?>
            /* Individual Slider Settings */
            <?php if (!empty($sliders)): ?>
                <?php
                // Get the first slider's settings as default
                $firstSlider = $sliders[0];
                $defaultHeight = $firstSlider['height_desktop'] ?? 500;
                $defaultTabletHeight = $firstSlider['height_tablet'] ?? 400;
                $defaultMobileHeight = $firstSlider['height_mobile'] ?? 300;
                $defaultPaddingTop = $firstSlider['padding_top'] ?? 90;
                $defaultPaddingBottom = $firstSlider['padding_bottom'] ?? 40;
                ?>

                /* Default slider settings */
                .hero-section {
                    height: <?php echo $defaultHeight; ?>px !important;
                    min-height: <?php echo $defaultHeight; ?>px !important;
                    padding-top: <?php echo $defaultPaddingTop; ?>px !important;
                    padding-bottom: <?php echo $defaultPaddingBottom; ?>px !important;
                }

                @media (max-width: 992px) {
                    .hero-section {
                        height: <?php echo $defaultTabletHeight; ?>px !important;
                        min-height: <?php echo $defaultTabletHeight; ?>px !important;
                    }
                }

                @media (max-width: 768px) {
                    .hero-section {
                        height: <?php echo $defaultMobileHeight; ?>px !important;
                        min-height: <?php echo $defaultMobileHeight; ?>px !important;
                    }
                }

                /* Individual slider overrides */
                <?php foreach ($sliders as $index => $slider): ?>
                    .hero-section[data-current-slide="<?php echo $index; ?>"] {
                        height: <?php echo $slider['height_desktop'] ?? 500; ?>px !important;
                        min-height: <?php echo $slider['height_desktop'] ?? 500; ?>px !important;
                        padding-top: <?php echo $slider['padding_top'] ?? 90; ?>px !important;
                        padding-bottom: <?php echo $slider['padding_bottom'] ?? 40; ?>px !important;
                    }

                    @media (max-width: 992px) {
                        .hero-section[data-current-slide="<?php echo $index; ?>"] {
                            height: <?php echo $slider['height_tablet'] ?? 400; ?>px !important;
                            min-height: <?php echo $slider['height_tablet'] ?? 400; ?>px !important;
                        }
                    }

                    @media (max-width: 768px) {
                        .hero-section[data-current-slide="<?php echo $index; ?>"] {
                            height: <?php echo $slider['height_mobile'] ?? 300; ?>px !important;
                            min-height: <?php echo $slider['height_mobile'] ?? 300; ?>px !important;
                        }
                    }
                <?php endforeach; ?>
            <?php endif; ?>
        <?php endif; ?>

        /* Content Alignment Settings */
        <?php if (!empty($sliders)): ?>
            <?php foreach ($sliders as $index => $slider): ?>
                .hero-text-slide[data-slide="<?php echo $index; ?>"] {
                    text-align: <?php echo $slider['content_alignment'] ?? 'center'; ?>;
                    align-items: <?php echo $slider['vertical_alignment'] === 'top' ? 'flex-start' : ($slider['vertical_alignment'] === 'bottom' ? 'flex-end' : 'center'); ?>;
                    justify-content: <?php echo $slider['content_alignment'] === 'left' ? 'flex-start' : ($slider['content_alignment'] === 'right' ? 'flex-end' : 'center'); ?>;
                }
            <?php endforeach; ?>
        <?php endif; ?>

        /* Enhanced Animation Styles */
        .hero-section {
            transition: height 0.3s ease;
        }

        .hero-text-slide {
            display: flex;
            flex-direction: column;
            height: 100%;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.6s ease;
        }

        .hero-text-slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .hero-text-slide.fade-out {
            opacity: 0;
            transform: translateX(-100%);
        }

        .hero-background img {
            transition: all 0.6s ease;
            opacity: 0;
        }

        .hero-background img.active {
            opacity: 1;
        }

        .hero-background img.zoom-in {
            transform: scale(1.1);
        }

        .hero-background img.zoom-out {
            transform: scale(0.9);
        }

        /* Enhanced Animation Keyframes */
        @keyframes bounceIn {
            0% { opacity: 0; transform: scale(0.3) translate3d(0, -30px, 0); }
            20% { opacity: 1; transform: scale(1.05) translate3d(0, -5px, 0); }
            40% { transform: scale(0.9) translate3d(0, 0, 0); }
            60% { transform: scale(1.02) translate3d(0, -2px, 0); }
            80% { transform: scale(0.98) translate3d(0, 0, 0); }
            100% { opacity: 1; transform: scale(1) translate3d(0, 0, 0); }
        }

        @keyframes bounceOut {
            0% { opacity: 1; transform: scale(1) translate3d(0, 0, 0); }
            20% { transform: scale(0.98) translate3d(0, -2px, 0); }
            40% { transform: scale(1.02) translate3d(0, 0, 0); }
            60% { opacity: 1; transform: scale(0.9) translate3d(0, -5px, 0); }
            100% { opacity: 0; transform: scale(0.3) translate3d(0, -30px, 0); }
        }

        /* Smooth transitions for all elements */
        .hero-text-slide, .hero-background img {
            transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
        }

        /* Universal Height Override */
        .hero-section.universal-height {
            height: auto !important;
        }
    </style>

    <!-- Dynamic Slider Overlay Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const heroSection = document.querySelector('.hero-section');
            const heroOverlay = document.querySelector('.hero-overlay');
            const backgroundImages = document.querySelectorAll('.hero-background img');
            const textSlides = document.querySelectorAll('.hero-text-slide');

            let currentSlide = 0;
            let slideInterval;
            let isPaused = false;

            // Get slider settings from first image (all slides share same settings)
            const firstImage = backgroundImages[0];
            const animationType = firstImage?.dataset.animationType || 'fade';
            const animationDuration = parseFloat(firstImage?.dataset.animationDuration || 5) * 1000;
            const autoPlay = firstImage?.dataset.autoPlay === '1';
            const pauseOnHover = firstImage?.dataset.pauseOnHover === '1';

            function updateOverlay() {
                const activeImage = document.querySelector('.hero-background img.active');
                if (activeImage && heroOverlay) {
                    const overlayColor = activeImage.dataset.overlayColor || '#000000';
                    const overlayOpacity = activeImage.dataset.overlayOpacity || '0.5';

                    // Convert hex to rgba
                    const hex = overlayColor.replace('#', '');
                    const r = parseInt(hex.substr(0, 2), 16);
                    const g = parseInt(hex.substr(2, 2), 16);
                    const b = parseInt(hex.substr(4, 2), 16);

                    heroOverlay.style.background = `rgba(${r}, ${g}, ${b}, ${overlayOpacity})`;
                }
            }

            // Smart text line breaking
            function applyTextBreaking() {
                const titles = document.querySelectorAll('.hero-title[data-break-type]');
                const descriptions = document.querySelectorAll('.hero-data[data-break-type]');

                function breakText(element) {
                    const breakType = element.dataset.breakType;
                    const breakLimit = parseInt(element.dataset.breakLimit);
                    const originalText = element.textContent.trim();

                    if (breakType === 'none') return;

                    let brokenText = '';
                    if (breakType === 'character') {
                        // Character-based breaking
                        for (let i = 0; i < originalText.length; i += breakLimit) {
                            if (i > 0) brokenText += '<br>';
                            brokenText += originalText.substr(i, breakLimit);
                        }
                    } else if (breakType === 'word') {
                        // Word-based breaking
                        const words = originalText.split(' ');
                        let currentLine = '';
                        let wordCount = 0;

                        words.forEach((word, index) => {
                            if (wordCount >= breakLimit && wordCount > 0) {
                                brokenText += currentLine + '<br>';
                                currentLine = word;
                                wordCount = 1;
                            } else {
                                currentLine += (currentLine ? ' ' : '') + word;
                                wordCount++;
                            }

                            if (index === words.length - 1) {
                                brokenText += currentLine;
                            }
                        });
                    }

                    if (brokenText) {
                        element.innerHTML = brokenText;
                    }
                }

                titles.forEach(breakText);
                descriptions.forEach(breakText);
            }

            // Animation functions
            function slideTransition(direction = 'next') {
                const currentImage = backgroundImages[currentSlide];
                const currentText = textSlides[currentSlide];

                // Calculate next slide
                const nextSlide = direction === 'next'
                    ? (currentSlide + 1) % backgroundImages.length
                    : (currentSlide - 1 + backgroundImages.length) % backgroundImages.length;

                const nextImage = backgroundImages[nextSlide];
                const nextText = textSlides[nextSlide];

                // Apply animation based on type
                switch (animationType) {
                    case 'fade':
                        currentImage.style.opacity = '0';
                        currentText.style.opacity = '0';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextImage.style.opacity = '1';
                            nextText.style.opacity = '1';
                        }, 300);
                        break;

                    case 'slide':
                        currentText.style.transform = 'translateX(-100%)';
                        nextText.style.transform = 'translateX(100%)';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.transform = 'translateX(0)';
                        }, 100);
                        break;

                    case 'zoom':
                        currentImage.style.transform = 'scale(1.2)';
                        currentText.style.transform = 'scale(0.8)';
                        currentText.style.opacity = '0';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            currentImage.style.transform = 'scale(1)';
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextImage.style.transform = 'scale(1)';
                            nextText.style.transform = 'scale(1)';
                            nextText.style.opacity = '1';
                        }, 300);
                        break;

                    case 'slideVertical':
                        currentText.style.transform = 'translateY(-100%)';
                        nextText.style.transform = 'translateY(100%)';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.transform = 'translateY(0)';
                        }, 100);
                        break;

                    case 'bounce':
                        currentText.style.animation = 'bounceOut 0.5s forwards';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.animation = 'bounceIn 0.5s forwards';
                        }, 250);
                        break;

                    case 'flip':
                        currentText.style.transform = 'rotateY(90deg)';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.transform = 'rotateY(0deg)';
                        }, 300);
                        break;

                    case 'rotate':
                        currentText.style.transform = 'rotate(180deg)';
                        currentText.style.opacity = '0';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.transform = 'rotate(0deg)';
                            nextText.style.opacity = '1';
                        }, 300);
                        break;

                    case 'blur':
                        currentImage.style.filter = 'blur(10px)';
                        currentText.style.filter = 'blur(5px)';
                        currentText.style.opacity = '0';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            currentImage.style.filter = 'none';
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextImage.style.filter = 'none';
                            nextText.style.filter = 'none';
                            nextText.style.opacity = '1';
                        }, 400);
                        break;

                    case 'scale':
                        currentText.style.transform = 'scale(1.5)';
                        currentText.style.opacity = '0';
                        setTimeout(() => {
                            currentImage.classList.remove('active');
                            currentText.classList.remove('active');
                            nextImage.classList.add('active');
                            nextText.classList.add('active');
                            nextText.style.transform = 'scale(1)';
                            nextText.style.opacity = '1';
                        }, 300);
                        break;

                    case 'none':
                        currentImage.classList.remove('active');
                        currentText.classList.remove('active');
                        nextImage.classList.add('active');
                        nextText.classList.add('active');
                }

                currentSlide = nextSlide;
                updateOverlay();
            }

            // Auto-play functionality
            function startAutoPlay() {
                if (autoPlay && backgroundImages.length > 1) {
                    slideInterval = setInterval(() => {
                        if (!isPaused) {
                            slideTransition('next');
                        }
                    }, animationDuration);
                }
            }

            function stopAutoPlay() {
                if (slideInterval) {
                    clearInterval(slideInterval);
                }
            }

            // Pause on hover
            if (pauseOnHover && heroSection) {
                heroSection.addEventListener('mouseenter', () => {
                    isPaused = true;
                });

                heroSection.addEventListener('mouseleave', () => {
                    isPaused = false;
                });
            }

            // Initialize
            updateOverlay();
            applyTextBreaking();
            startAutoPlay();

            // Add data attributes for slide identification
            textSlides.forEach((slide, index) => {
                slide.setAttribute('data-slide', index);
            });
        });
    </script>
</body>
</html>
